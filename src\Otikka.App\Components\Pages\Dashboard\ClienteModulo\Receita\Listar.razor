﻿@using Otikka.App.Components.Pages.Dashboard.ClienteModulo.Cliente.Componentes
@using Otikka.Application.Models
@using Otikka.Application.Features.Pessoa.Queries.ListReceitas
@using Otikka.Application.Features.Pessoa.Queries.GetCliente
@using Otikka.Application.Features.Pessoa.Commands.DeleteReceita
@using Otikka.Domain.Entities.PessoaModulo
@using FluentResults
@using Wolverine
@attribute [Route(Application.Routes.ReceitaListar)]
@inherits PageBase
@inject ILogger<Otikka.App.Components.Pages.Dashboard.ClienteModulo.Receita.Listar> Logger


<PageTitle>
    Receita - Lista
</PageTitle>
<div class="card">
    <div class="card-header align-items-center d-flex">
        <h4 class="card-title mb-0 flex-grow-1">
            <a href="@Application.Routes.ClienteListar" class="btn btn-info btn-sm material-shadow-none">
                <i class="ri-arrow-left-circle-line align-middle me-1"></i> Voltar
            </a>
        </h4>
        <h4 class="card-title mb-0 flex-grow-1">
            <strong>Receitas</strong>
        </h4>
        <div class="flex-shrink-0">
            <a href="@Application.Routes.GerarRota(Application.Routes.ReceitaCadastrar, ClienteId.ToString())" class="btn btn-primary btn-sm material-shadow-none">
                <i class="ri-file-list-3-line align-middle"></i> Cadastrar
            </a>
        </div>
    </div><!-- end card header -->

    <div class="card-body">
        <div class="col-lg-12">
            <ClienteResumo Pessoa="Cliente" />
        </div>
        <div class="col-lg-6">
            <div class="input-group">
                <input type="text" class="form-control" placeholder="Digite sua pesquisa" @bind="SearchWord">
                <button class="btn btn-outline-success material-shadow-none" type="button" @onclick="OnSearch">Pesquisar</button>
            </div>
        </div>
        <div class="table-responsive mt-4 table-margin-width-24">
            <table class="table table-borderless table-centered align-middle table-nowrap mb-0">
                <thead class="text-muted table-light">
                    <tr>
                        <th scope="col">Nome profissional</th>
                        <th scope="col">Data de Cadastro</th>
                        <th scope="col">Data de Validade</th>
                        <th scope="col">Ação</th>
                    </tr>
                </thead>
                <tbody>
                    @if (Paginated == null)
                    {
                        <tr>
                            <td colspan="4">Carregando...</td>
                        </tr>
                    }
                    else if (Paginated.Items.Count == 0)
                    {
                        <tr>
                            <td colspan="4">Nenhum registro!</td>
                        </tr>
                    }

                    else
                    {
                        @foreach (var item in Paginated.Items)
                        {
                            <tr>
                                <td>
                                    @item.NomeProfissional
                                </td>
                                <td>
                                    @(item.DataCriacao.ToString("dd/MM/yyyy"))
                                </td>
                                <td>
                                    @(item.DataValidade.HasValue? item.DataValidade.Value.ToString("dd/MM/yyyy") : "-")
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="@Application.Routes.GerarRota(Application.Routes.ReceitaEditar, ClienteId.ToString(), item.Id.ToString())" class="btn btn-sm btn-soft-info ms-2">
                                            <i class="ri-edit-line"></i>
                                        </a>
                                        <a class="btn btn-sm btn-soft-danger" @onclick="() => Excluir(item)">
                                                <i class="ri-delete-bin-line"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
        <Pagination Paginated="@Paginated" OnPageChanged="@OnPageChanged" />
    </div>
</div>

@code {

    [Parameter] public Guid ClienteId { get; set; }
    public Pessoa? Cliente { get; set; }

    private int PageIndex = 1;
    private int PageSize;
    private string SearchWord = string.Empty;
    private PaginatedList<Otikka.Domain.Entities.PessoaModulo.Receita>? Paginated;

    protected override void OnInitialized()
    {
        PageSize = Configuration.GetValue<int>("Pagination:PageSize");
    }

    protected override async Task OnParametersSetAsync()
    {
        try
        {
            var empresaId = await GetEmpresaIdAsync();

            // Carregar dados do cliente usando CQRS
            var clienteQuery = new GetCliente
            {
                Id = ClienteId,
                EmpresaId = empresaId
            };

            var clienteResult = await MessageBus.InvokeAsync<Result<Pessoa>>(clienteQuery);
            if (clienteResult.IsSuccess)
            {
                Cliente = clienteResult.Value;
            }
            else
            {
                await AlertService.ShowError("Erro", "Cliente não encontrado");
                NavigationManager.NavigateTo(Application.Routes.ClienteListar);
                return;
            }

            await LoadDataAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao carregar dados do cliente {ClienteId}: {Message}", ClienteId, ex.Message);
            await AlertService.ShowError("Erro", $"Erro inesperado: {ex.Message}");
        }
    }

    private async Task Excluir(Receita entidade)
    {
        if (await AlertService.ShowConfirmDelete())
        {
            try
            {
                var empresaId = await GetEmpresaIdAsync();

                var command = new DeleteReceita
                {
                    Id = entidade.Id,
                    EmpresaId = empresaId
                };

                var result = await MessageBus.InvokeAsync<Result>(command);

                if (result.IsSuccess)
                {
                    await AlertService.ShowSuccessMessage("Receita excluída com sucesso!");
                    await LoadDataAsync();
                }
                else
                {
                    await AlertService.ShowError("Erro", result.Errors.FirstOrDefault()?.Message ?? "Erro ao excluir receita");
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Erro inesperado ao excluir receita {ReceitaId}: {Message}", entidade.Id, ex.Message);
                await AlertService.ShowError("Erro", $"Erro inesperado: {ex.Message}");
            }
        }
    }

    private async Task OnSearch()
    {
        PageIndex = 1;
        await LoadDataAsync();
    }

    private async Task OnPageChanged(int pageNumber)
    {
        PageIndex = pageNumber;
        await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        try
        {
            var empresaId = await GetEmpresaIdAsync();

            var query = new ListReceitas
            {
                ClienteId = ClienteId,
                EmpresaId = empresaId,
                PageIndex = PageIndex,
                PageSize = PageSize,
                SearchWord = SearchWord
            };

            var result = await MessageBus.InvokeAsync<Result<PaginatedList<Receita>>>(query);

            if (result.IsSuccess)
            {
                Paginated = result.Value;
            }
            else
            {
                await AlertService.ShowError("Erro", result.Errors.FirstOrDefault()?.Message ?? "Erro ao carregar receitas");
                Paginated = new PaginatedList<Receita>(new List<Receita>(), 1, 1);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao carregar receitas do cliente {ClienteId}: {Message}", ClienteId, ex.Message);
            await AlertService.ShowError("Erro", $"Erro inesperado: {ex.Message}");
            Paginated = new PaginatedList<Receita>(new List<Receita>(), 1, 1);
        }

        StateHasChanged();
    }

}