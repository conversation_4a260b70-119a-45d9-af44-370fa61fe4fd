@using Otikka.App.Components.Pages.Dashboard.ColaboradorModulo.Colaborador.Componentes
@using Otikka.Application.Features.Colaborador.Commands.CreateColaborador
@using Otikka.Application.Features.Colaborador.Commands.ReativarColaborador
@using Wolverine
@attribute [Route(Application.Routes.ColaboradorCadastrar)]
@inherits PageBase

<PageTitle>
    Colaborador - Cadastro
</PageTitle>
<ColaboradorForm @ref="colaboradorForm" EhEdicao="@false" Usuario="Usuario" OnSave="Save"/>

@code {

    [SupplyParameterFromQuery] private string? ReturnUrl { get; set; }
    [SupplyParameterFromForm] private Usuario Usuario { get; set; } = new();
    private ColaboradorForm? colaboradorForm;

    private async Task Save()
    {
        await ProcessingChange(true);

        try
        {
            var empresaId = await GetEmpresaIdAsync();

            var command = new CreateColaborador
            {
                Nome = Usuario.Nome,
                Email = Usuario.Email,
                EmpresaId = empresaId,
                Perfil = colaboradorForm?.Perfil ?? TipoPerfilColaborador.Vendedor,
                // Campos de auditoria
                CriadoPorId = await GetUsuarioIdLoggedAsync(),
                DataCriacao = DateTime.UtcNow
            };

            var result = await MessageBus.InvokeAsync<Result>(command);

            if (result.IsSuccess)
            {
                await AlertService.ShowSuccessMessage(Application.Messages.SucessoSalvar);
                NavigationManager.NavigateTo(Application.Routes.ColaboradorListar);
            }
            else
            {
                await AlertService.ShowError("Opps!", result.Errors.First()?.Message ?? "Erro ao cadastrar colaborador");
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowError("Opps!", $"Erro inesperado: {ex.Message}");
        }
        finally
        {
            await ProcessingChange(false);
        }
    }
}