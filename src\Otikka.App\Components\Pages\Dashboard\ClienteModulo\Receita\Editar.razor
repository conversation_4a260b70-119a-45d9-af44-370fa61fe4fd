﻿@using Otikka.App.Components.Pages.Dashboard.ClienteModulo.Receita.Componentes
@using Otikka.Application.Common.Extensions
@using Otikka.Application.Contracts.Persistence
@attribute [Route(Application.Routes.ReceitaEditar)]
@rendermode InteractiveServer


@inherits PageBase
@inject IReceitaRepositorio ReceitaRepositorio

<PageTitle>
    Receita - Atualizar
</PageTitle>

<ReceitaForm EhEdicao="@true" Receita="Receita" OnSave="Submit" />

@code {
    
    [Parameter] public Guid Id { get; set; }
    [Parameter] public Guid ClienteId { get; set; }

    [SupplyParameterFromForm]
    private Otikka.Domain.Entities.PessoaModulo.Receita Receita { get; set; } = new();


    protected override async Task OnParametersSetAsync()
    {
        var receita = await ReceitaRepositorio.Obter(Id);

        if (receita is not null)
            Receita = receita;
        
        if (Receita.Id == Guid.Empty)
        {
            await JsRuntime.InvokeVoidAsync("alert", "A receita selecionada não existe no banco!");
            NavigationManager.NavigateTo(Application.Routes.GerarRota(Application.Routes.ReceitaListar, ClienteId.ToString()));
            return;
        }
        var EmpresaId = await GetEmpresaIdAsync();

        if (Receita?.EmpresaId != EmpresaId)
        {
            await JsRuntime.InvokeVoidAsync("alert", $"A receita não pertence a empresa!");
            //TODO - Trocar rota pelo Gerar Rota do Application
            NavigationManager.NavigateTo(Application.Routes.GerarRota(Application.Routes.ReceitaListar, ClienteId.ToString()));
        }
    }
    
    private async Task Submit()
    {
        // Validação para não permitir salvar receita com todos os campos zerados
        if (Receita != null && !Receita.HasValidMeasurements())
        {
            await AlertService.ShowError("Erro de Validação", 
                "Não é possível salvar uma receita com todos os campos de medição zerados. " +
                "Por favor, preencha pelo menos um valor diferente de zero.");
            return;
        }

        try
        {
            if (Receita != null)
            {
                Receita.DataAtualizacao = DateTimeOffset.Now;
                await ReceitaRepositorio.Atualizar(Receita);
                await AlertService.ShowSuccessMessage(Application.Messages.SucessoSalvar);
                NavigationManager.NavigateTo(Application.Routes.GerarRota(Application.Routes.ReceitaListar, ClienteId.ToString()));
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowError("Erro", $"Erro ao atualizar receita: {ex.Message}");
        }
    }
}