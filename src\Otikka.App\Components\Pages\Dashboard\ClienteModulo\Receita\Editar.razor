﻿@using Otikka.App.Components.Pages.Dashboard.ClienteModulo.Receita.Componentes
@using Otikka.Application.Features.Pessoa.Commands.UpdateReceita
@using Otikka.Application.Features.Pessoa.Queries.GetReceita
@using FluentResults
@using Wolverine
@attribute [Route(Application.Routes.ReceitaEditar)]
@rendermode InteractiveServer

@inherits PageBase

<PageTitle>
    Receita - Atualizar
</PageTitle>

<ReceitaForm EhEdicao="@true" Receita="Receita" OnSave="Submit" />

@code {

    [Parameter] public Guid Id { get; set; }
    [Parameter] public Guid ClienteId { get; set; }

    [SupplyParameterFromForm]
    private UpdateReceita Receita { get; set; } = new();


    protected override async Task OnParametersSetAsync()
    {
        try
        {
            var empresaId = await GetEmpresaIdAsync();

            var query = new GetReceita
            {
                Id = Id,
                EmpresaId = empresaId
            };

            var result = await MessageBus.InvokeAsync<Result<Otikka.Domain.Entities.PessoaModulo.Receita>>(query);

            if (result.IsSuccess && result.Value != null)
            {
                // Mapear a entidade para o comando de atualização
                var receita = result.Value;
                Receita = new UpdateReceita
                {
                    Id = receita.Id,
                    ClienteId = receita.ClienteId,
                    EmpresaId = receita.EmpresaId,
                    NomeProfissional = receita.NomeProfissional,
                    DataValidade = receita.DataValidade,
                    Adicao = receita.Adicao,
                    EsfericoOlhoDireitoPerto = receita.EsfericoOlhoDireitoPerto,
                    CilindricoOlhoDireitoPerto = receita.CilindricoOlhoDireitoPerto,
                    EixoOlhoDireitoPerto = receita.EixoOlhoDireitoPerto,
                    AlturaOlhoDireitoPerto = receita.AlturaOlhoDireitoPerto,
                    DNPOlhoDireitoPerto = receita.DNPOlhoDireitoPerto,
                    EsfericoOlhoDireitoLonge = receita.EsfericoOlhoDireitoLonge,
                    CilindricoOlhoDireitoLonge = receita.CilindricoOlhoDireitoLonge,
                    EixoOlhoDireitoLonge = receita.EixoOlhoDireitoLonge,
                    AlturaOlhoDireitoLonge = receita.AlturaOlhoDireitoLonge,
                    DNPOlhoDireitoLonge = receita.DNPOlhoDireitoLonge,
                    EsfericoOlhoEsquerdoPerto = receita.EsfericoOlhoEsquerdoPerto,
                    CilindricoOlhoEsquerdoPerto = receita.CilindricoOlhoEsquerdoPerto,
                    EixoOlhoEsquerdoPerto = receita.EixoOlhoEsquerdoPerto,
                    AlturaOlhoEsquerdoPerto = receita.AlturaOlhoEsquerdoPerto,
                    DNPOlhoEsquerdoPerto = receita.DNPOlhoEsquerdoPerto,
                    EsfericoOlhoEsquerdoLonge = receita.EsfericoOlhoEsquerdoLonge,
                    CilindricoOlhoEsquerdoLonge = receita.CilindricoOlhoEsquerdoLonge,
                    EixoOlhoEsquerdoLonge = receita.EixoOlhoEsquerdoLonge,
                    AlturaOlhoEsquerdoLonge = receita.AlturaOlhoEsquerdoLonge,
                    DNPOlhoEsquerdoLonge = receita.DNPOlhoEsquerdoLonge,
                    ImagemReceitaUrl = receita.ImagemReceitaUrl,
                    DataCriacao = receita.DataCriacao,
                    CriadoPorId = receita.CriadoPorId
                };
            }
            else
            {
                await AlertService.ShowError("Erro", result.Errors.FirstOrDefault()?.Message ?? "Receita não encontrada");
                NavigationManager.NavigateTo(Application.Routes.GerarRota(Application.Routes.ReceitaListar, ClienteId.ToString()));
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao carregar receita {ReceitaId}: {Message}", Id, ex.Message);
            await AlertService.ShowError("Erro", $"Erro inesperado: {ex.Message}");
            NavigationManager.NavigateTo(Application.Routes.GerarRota(Application.Routes.ReceitaListar, ClienteId.ToString()));
        }
    }
    
    private async Task Submit()
    {
        try
        {
            Logger.LogInformation("Iniciando atualização de receita: {ReceitaId} para cliente: {ClienteId}", Id, ClienteId);

            var result = await MessageBus.InvokeAsync<Result>(Receita);

            if (result.IsSuccess)
            {
                await AlertService.ShowSuccessMessage(Application.Messages.SucessoSalvar);
                NavigationManager.NavigateTo(Application.Routes.GerarRota(Application.Routes.ReceitaListar, ClienteId.ToString()));
            }
            else
            {
                await AlertService.ShowError("Erro de Validação", result.Errors.FirstOrDefault()?.Message ?? "Erro ao atualizar receita");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro inesperado ao atualizar receita {ReceitaId}: {Message}", Id, ex.Message);
            await AlertService.ShowError("Erro", $"Erro inesperado: {ex.Message}");
        }
    }
}