namespace Otikka.Domain.Entities.PessoaModulo;

public class Receita : EmpresaEntidadeBase
{
    public string? NomeProfissional { get; set; }
    public DateTimeOffset? DataValidade { get; set; }
    public float? Adicao { get; set; }

    public float? EsfericoOlhoDireitoPerto { get; set; } = 0;
    public float? CilindricoOlhoDireitoPerto { get; set; } = 0;
    public int? EixoOlhoDireitoPerto { get; set; } = 0;
    public float? AlturaOlhoDireitoPerto { get; set; } = 0;
    public float? DNPOlhoDireitoPerto { get; set; } = 0;

    public float? EsfericoOlhoDireitoLonge { get; set; } = 0;
    public float? CilindricoOlhoDireitoLonge { get; set; } = 0;
    public int? EixoOlhoDireitoLonge { get; set; } = 0;
    public float? AlturaOlhoDireitoLonge { get; set; } = 0;
    public float? DNPOlhoDireitoLonge { get; set; } = 0;

    public float? EsfericoOlhoEsquerdoPerto { get; set; } = 0;
    public float? CilindricoOlhoEsquerdoPerto { get; set; } = 0;
    public int? EixoOlhoEsquerdoPerto { get; set; } = 0;
    public float? AlturaOlhoEsquerdoPerto { get; set; } = 0;
    public float? DNPOlhoEsquerdoPerto { get; set; } = 0;

    public float? EsfericoOlhoEsquerdoLonge { get; set; } = 0;
    public float? CilindricoOlhoEsquerdoLonge { get; set; } = 0;
    public int? EixoOlhoEsquerdoLonge { get; set; } = 0;
    public float? AlturaOlhoEsquerdoLonge { get; set; } = 0;
    public float? DNPOlhoEsquerdoLonge { get; set; } = 0;

    public string? ImagemReceitaUrl { get; set; }

    public Guid ClienteId { get; set; }
    public Pessoa Cliente { get; set; } = null!;

    public Guid? OrdemServicoId { get; set; }
    public OrdemServico? OrdemServico { get; set; }
}