@using Otikka.App.Components.Pages.Dashboard.ClienteModulo.Receita.Componentes
@using Otikka.Application.Common.Extensions
@using Otikka.Application.Contracts.Persistence
@attribute [Route(Application.Routes.ReceitaCadastrar)]
@inherits PageBase
@inject IReceitaRepositorio ReceitaRepositorio

<ReceitaForm EhEdicao="@false" Receita="Receita" OnSave="Save" />

@code {

    [Parameter] public Guid ClienteId { get; set; }
    public Otikka.Domain.Entities.PessoaModulo.Receita? Receita { get; set; }

    protected override void OnInitialized()
    {
        Receita = new Otikka.Domain.Entities.PessoaModulo.Receita() { DataValidade = DateTime.Now.AddYears(1) };
    }

    private async Task Save()
    {
        // Validação para não permitir salvar receita com todos os campos zerados
        if (Receita != null && !Receita.HasValidMeasurements())
        {
            await AlertService.ShowError("Erro de Validação", 
                "Não é possível salvar uma receita com todos os campos de medição zerados. " +
                "Por favor, preencha pelo menos um valor diferente de zero.");
            return;
        }

        Receita!.EmpresaId = await GetEmpresaIdAsync();
        Receita.ClienteId = ClienteId;
        Receita.DataCriacao = DateTimeOffset.Now;
        Receita.CriadoPorId = await GetUsuarioIdLoggedAsync();

        try
        {
            await ReceitaRepositorio.Cadastrar(Receita);
            await AlertService.ShowSuccessMessage(Application.Messages.SucessoSalvar);
            NavigationManager.NavigateTo(Application.Routes.GerarRota(Application.Routes.ReceitaListar, ClienteId.ToString()));
        }
        catch (Exception ex)
        {
            await AlertService.ShowError("Erro", $"Erro ao salvar receita: {ex.Message}");
        }
    }
}