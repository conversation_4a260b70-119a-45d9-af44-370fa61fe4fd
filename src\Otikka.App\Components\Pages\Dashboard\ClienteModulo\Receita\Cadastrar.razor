@using Otikka.App.Components.Pages.Dashboard.ClienteModulo.Receita.Componentes
@using Otikka.Application.Features.Pessoa.Commands.CreateReceita
@using FluentResults
@using Wolverine
@attribute [Route(Application.Routes.ReceitaCadastrar)]
@inherits PageBase

<ReceitaForm EhEdicao="@false" Receita="Receita" OnSave="Save" />

@code {

    [Parameter] public Guid ClienteId { get; set; }

    [SupplyParameterFromForm]
    private CreateReceita Receita { get; set; } = new();

    protected override async Task OnInitializedAsync()
    {
        Receita.ClienteId = ClienteId;
        Receita.EmpresaId = await GetEmpresaIdAsync();
        Receita.DataValidade = DateTime.Now.AddYears(1);
        Receita.CriadoPorId = await GetUsuarioIdLoggedAsync();
    }

    private async Task Save()
    {
        try
        {
            Logger.LogInformation("Iniciando cadastro de receita para cliente: {ClienteId}", ClienteId);

            var result = await MessageBus.InvokeAsync<Result>(Receita);

            if (result.IsSuccess)
            {
                await AlertService.ShowSuccessMessage(Application.Messages.SucessoSalvar);
                NavigationManager.NavigateTo(Application.Routes.GerarRota(Application.Routes.ReceitaListar, ClienteId.ToString()));
            }
            else
            {
                await AlertService.ShowError("Erro de Validação", result.Errors.FirstOrDefault()?.Message ?? "Erro ao cadastrar receita");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro inesperado ao cadastrar receita para cliente {ClienteId}: {Message}", ClienteId, ex.Message);
            await AlertService.ShowError("Erro", $"Erro inesperado: {ex.Message}");
        }
    }
}