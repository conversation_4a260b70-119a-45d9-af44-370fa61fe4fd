<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Otikka.Application\Otikka.Application.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AWSSDK.S3" Version="4.0.4.2" />
    <PackageReference Include="Evolution.Client.CSharp" Version="1.0.3" />
    <PackageReference Include="Hangfire" Version="1.8.20" />
    <PackageReference Include="Hangfire.PostgreSql" Version="1.20.12" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.7" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
  </ItemGroup>

</Project>
